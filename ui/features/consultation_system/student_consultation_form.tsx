import React from 'react'
import { createRoot } from 'react-dom/client'
import StudentConsultationForm from './react/StudentConsultationForm'

// Initialize the Student Consultation Form
const initializeStudentConsultationForm = () => {
  const container = document.getElementById('student-consultation-form-container')
  if (!container) {
    console.error('Student consultation form container not found')
    return
  }

  const envData = window.ENV?.STUDENT_CONSULTATION_FORM
  if (!envData) {
    console.error('Student consultation form environment data not found')
    return
  }

  const root = createRoot(container)
  root.render(
    <StudentConsultationForm
      currentUserId={envData.current_user_id}
      studentInfo={envData.student_info}
      availableFaculty={envData.available_faculty || []}
    />
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeStudentConsultationForm)
} else {
  initializeStudentConsultationForm()
}

export default initializeStudentConsultationForm
